const fs = require("fs/promises");
const path = require("path");
const fetch = require("node-fetch");
const deepEqual = require("fast-deep-equal");
const yaml = require("yaml");
const { <PERSON>alar, Document } = yaml;
require("dotenv").config();

const TARGET_URL = "https://api.onesource.io/v1/ethereum/graphql";
const API_TOKEN = process.env.ONESOURCE_API_TOKEN || "";

const BASE_DIR = path.join(process.cwd(), "schema-overrides");
const CACHE_PATH = path.join(process.cwd(), ".cache", "introspectionSchema.json"); // Path to save schema

// Create directory if it doesn't exist
async function ensureDir(dir) {
  try {
    await fs.access(dir);
  } catch {
    await fs.mkdir(dir, { recursive: true });
    console.log(`📁 Directory created: ${dir}`);
  }
}

// Check if the cache for schema exists
async function cacheExists() {
  try {
    await fs.access(CACHE_PATH);
    return true;
  } catch {
    return false;
  }
}

// Check if schema-overrides directory (with YAML files) exists and has meaningful content
async function schemaOverridesExists() {
  try {
    await fs.access(BASE_DIR);
    // Check if directory has any content (recursively check subdirectories)
    const entries = await fs.readdir(BASE_DIR);

    // Filter out hidden files and check if there are any meaningful directories/files
    const meaningfulEntries = entries.filter(entry => !entry.startsWith('.'));

    if (meaningfulEntries.length === 0) {
      return false;
    }

    // Check if any of the expected subdirectories exist and have content
    const expectedDirs = ['queries', 'mutations', 'subscriptions', 'types'];
    for (const dir of expectedDirs) {
      const dirPath = path.join(BASE_DIR, dir);
      try {
        const stat = await fs.stat(dirPath);
        if (stat.isDirectory()) {
          const dirEntries = await fs.readdir(dirPath);
          if (dirEntries.length > 0) {
            return true; // Found at least one non-empty expected directory
          }
        }
      } catch {
        // Directory doesn't exist, continue checking
      }
    }

    // Check if there are any YAML files directly in the base directory
    const yamlFiles = meaningfulEntries.filter(entry => entry.endsWith('.yaml') || entry.endsWith('.yml'));
    return yamlFiles.length > 0;

  } catch {
    return false;
  }
}

// Load the previously saved schema from cache if available
async function loadCachedSchema() {
  try {
    const data = await fs.readFile(CACHE_PATH, "utf8");
    return JSON.parse(data);
  } catch {
    return null; // No cache found
  }
}

// Save the current schema to cache for later comparison
async function saveSchemaToCache(schema) {
  await ensureDir(path.dirname(CACHE_PATH));
  await fs.writeFile(CACHE_PATH, JSON.stringify(schema, null, 2), "utf8");
}

// Recursively remove all 'description' fields from schema object
// This is done so that schema comparison ignores description text changes,
// focusing only on structural changes like added/removed fields or types
function stripDescriptions(obj) {
  if (Array.isArray(obj)) {
    return obj.map(stripDescriptions);
  }
  if (obj && typeof obj === "object") {
    const newObj = {};
    for (const key in obj) {
      if (key !== "description") {
        newObj[key] = stripDescriptions(obj[key]);
      }
    }
    return newObj;
  }
  return obj;
}

// Check if any of the main root schema types changed:
// queryType, mutationType, subscriptionType - either added, removed, or renamed
function topLevelChanged(a, b) {
  const keys = ["queryType", "mutationType", "subscriptionType"];
  for (const key of keys) {
    const aVal = a[key];
    const bVal = b[key];

    // If one exists but the other doesn't, schema changed
    if ((aVal && !bVal) || (!aVal && bVal)) return true;

    // If the name of the type changed, schema changed
    if (aVal?.name !== bVal?.name) return true;
  }
  return false;
}

// Fetch GraphQL schema via introspection to get full schema including types, fields, args, enums, etc.
const INTROSPECTION_QUERY = `
  query IntrospectionQuery {
    __schema {
      queryType { name }
      types {
        kind
        name
        description
        fields(includeDeprecated: true) {
          name
          description
          args {
            name
            description
          }
          type {
            kind
            name
            ofType {
              kind
              name
              ofType {
                kind
                name
              }
            }
          }
        }
        inputFields {
          name
          description
        }
        enumValues(includeDeprecated: true) {
          name
          description
        }
      }
    }
  }
`;

// Fetch schema from GraphQL endpoint using introspection query
async function fetchSchema() {
  // Create an AbortController to implement timeout
  const controller = new AbortController();
  // Set a 30-second timeout to abort the request if it takes too long
  const timeout = setTimeout(() => controller.abort(), 30_000);

  try {
    const res = await fetch(TARGET_URL, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "x-bp-token": API_TOKEN,
      },
      body: JSON.stringify({
        query: INTROSPECTION_QUERY,
      }),
    });

    // Clear timeout once response is received
    clearTimeout(timeout);

    const json = await res.json();

    if (json.errors) {
      throw new Error("Failed to fetch schema: " + JSON.stringify(json.errors));
    }

    // Validate schema data exists and has the expected structure
    if (!json.data || !json.data.__schema) {
      throw new Error("Invalid schema data received from GraphQL endpoint");
    }

    if (!Array.isArray(json.data.__schema.types)) {
      throw new Error("Schema types missing or invalid");
    }

    // Return the fetched schema for further processing
    return json.data.__schema;
    } catch (err) {
    // Clear timeout if an error occurs
    clearTimeout(timeout);

    if (err.name === "AbortError") {
      console.error("Schema fetch timed out after 30 seconds.");
    } else {
      console.error("Failed to fetch schema:", err);
    }
    process.exit(1);
  }
}

// Map GraphQL operation type to folder for saving YAML files
function operationTypeToDir(opType) {
    const map = {
    queryType: "queries",
    mutationType: "mutations",
    subscriptionType: "subscriptions",
  };
  return map[opType] || null;
}

// Map GraphQL type kind to folder name for saving YAML files
function getTypeDir(kind) {
  const map = {
    OBJECT: "types/objects",
    INTERFACE: "types/interfaces",
    SCALAR: "types/scalars",
    ENUM: "types/enums",
    INPUT_OBJECT: "types/inputs",
  };
  return map[kind] || null;
}

// Use literal block style (|) for multiline descriptions in YAML files
function applyLiteralStyleToDescriptions(obj) {
  if (typeof obj !== "object" || obj === null) return;

  for (const key in obj) {
    const value = obj[key];

    // For description strings with new lines, use literal block style
    if (
      key === "description" &&
      typeof value === "string" &&
      value.includes("\n")
    ) {
      obj[key] = new Scalar(value);
      obj[key].type = yaml.Scalar.BLOCK_LITERAL;
    }

    // Recursively apply style to nested objects
    if (typeof value === "object") {
      applyLiteralStyleToDescriptions(value);
    }
  }
}

// Merge old manual descriptions with new schema data and write YAML file
// This way, manual edits to descriptions in YAML files are preserved
async function writeYamlFile(dir, filename, data) {
  await ensureDir(dir);
  const filepath = path.join(dir, filename);

  let existing = {};
  try {
    const content = await fs.readFile(filepath, "utf8");
    existing = yaml.parse(content) || {};
  } catch {
    // File not found, will create a new one
  }

  const isNonEmptyString = (val) =>
    typeof val === "string" && val.trim() !== "";

  // Merge existing descriptions from old file into new data to preserve manual edits
  for (const key of Object.keys(data)) {
    const newType = data[key];
    const oldType = existing[key];
    if (!oldType) continue;
    // Merge top-level description if new is missing or empty
    if (
      !isNonEmptyString(newType.description) &&
      isNonEmptyString(oldType.description)
    ) {
      newType.description = oldType.description;
    }

    // Merge field descriptions
    if (oldType.fields && newType.fields) {
      for (const fieldName of Object.keys(newType.fields)) {
        const newField = newType.fields[fieldName];
        const oldField = oldType.fields[fieldName];
        if (!oldField) continue;
        if (
          !isNonEmptyString(newField.description) &&
          isNonEmptyString(oldField.description)
        ) {
          newField.description = oldField.description;
        }

        // For Query fields: merge args descriptions
        if (key === "Query" && oldField.args && newField.args) {
          for (const argName of Object.keys(newField.args)) {
            const oldArgDesc = oldField.args[argName];
            const newArgDesc = newField.args[argName];
            if (!isNonEmptyString(newArgDesc) && isNonEmptyString(oldArgDesc)) {
              newField.args[argName] = oldArgDesc;
            }
          }
        }
      }
    }

    // Merge inputFields descriptions (for INPUT_OBJECT)
    if (oldType.inputFields && newType.inputFields) {
      for (const inputName of Object.keys(newType.inputFields)) {
        const newInputDesc = newType.inputFields[inputName]?.description;
        const oldInputDesc = oldType.inputFields[inputName]?.description;
        if (!isNonEmptyString(newInputDesc) && isNonEmptyString(oldInputDesc)) {
          newType.inputFields[inputName].description = oldInputDesc;
        }
      }
    }

    // Merge enum values descriptions (for ENUM)
    if (oldType.values && newType.values) {
      for (const valName of Object.keys(newType.values)) {
        const newValDesc = newType.values[valName]?.description;
        const oldValDesc = oldType.values[valName]?.description;
        if (!isNonEmptyString(newValDesc) && isNonEmptyString(oldValDesc)) {
          newType.values[valName].description = oldValDesc;
        }
      }
    }
  }

  // Format multiline descriptions in YAML for readability
  applyLiteralStyleToDescriptions(data);

  const doc = new Document(data);
  const yamlStr = String(doc);

  await fs.writeFile(filepath, yamlStr, "utf8");
}

// Generate YAML files for root operations like Query fields
// For each query field, a YAML file is generated with field descriptions and argument descriptions
async function generateOperationYaml(schema, operationType) {
  if (!schema[operationType]) return;

  const typeName = schema[operationType].name;
  if (!typeName) return;

  const type = schema.types.find((t) => t.name === typeName);
  if (!type || !type.fields) return;

  const outDir = path.join(BASE_DIR, operationTypeToDir(operationType));
  await ensureDir(outDir);

  // Generate a YAML file for each query field preserving argument descriptions
  await Promise.all(
    type.fields.map(async (field) => {
      const yamlData = {
        Query: {
          fields: {
            [field.name]: {
              description: field.description || "",
              args: {},
            },
          },
        },
      };

      if (field.args && field.args.length) {
        for (const arg of field.args) {
          yamlData.Query.fields[field.name].args[arg.name] = arg.description || "";
        }
      }

      const filename = `${field.name}.yaml`;
      await writeYamlFile(outDir, filename, yamlData);
    })
  );
}

// Generate YAML files for all other GraphQL types (Objects, Enums, Inputs, etc.)
// Descriptions are preserved and can be manually edited and merged on re-generation
async function generateTypeYaml(type) {
  const dir = getTypeDir(type.kind);
  if (!dir) return;

  const outDir = path.join(BASE_DIR, dir);
  await ensureDir(outDir);

  const typeData = {
    description: "",
  };

  // Prepare fields/values/inputFields structure depending on type kind
  if (type.kind === "OBJECT" || type.kind === "INTERFACE") {
    typeData.fields = {};
    if (type.fields) {
      for (const field of type.fields) {
        typeData.fields[field.name] = {
          description: "",
        };
      }
    }
  } else if (type.kind === "ENUM") {
    typeData.values = {};
    if (type.enumValues) {
      for (const val of type.enumValues) {
        typeData.values[val.name] = { description: "" };
      }
    }
  } else if (type.kind === "INPUT_OBJECT") {
    typeData.inputFields = {};
    if (type.inputFields) {
      for (const input of type.inputFields) {
        typeData.inputFields[input.name] = { description: "" };
      }
    }
  } else if (type.kind === "SCALAR") {
    // nothing to add - scalars only have description
  }

  // Root key is the type name
  const yamlData = {
    [type.name]: typeData,
  };

  const filename = `${type.name}.yaml`;
  await writeYamlFile(outDir, filename, yamlData);
}

// Sort schema objects, fields, arguments, enum values by name to ensure
// consistent order before comparing schemas to avoid false positive differences
function sortSchema(schema) {
  if (!schema.types) return schema;

  // Create a deep copy to avoid mutating the original schema
  const newSchema = {
    ...schema,
    types: schema.types.map((t) => ({
      ...t,
      // Sort fields by name for consistent ordering
      fields: t.fields
        ? [...t.fields]
            .sort((a, b) => a.name.localeCompare(b.name))
            .map((f) => ({
              ...f,
              // Sort arguments of each field by name for consistency
              args: f.args ? [...f.args].sort((a, b) => a.name.localeCompare(b.name)) : undefined,
            }))
        : undefined,
      // Sort input fields by name for consistent ordering
      inputFields: t.inputFields ? [...t.inputFields].sort((a, b) => a.name.localeCompare(b.name)) : undefined,
      // Sort enum values by name for consistent ordering
      enumValues: t.enumValues ? [...t.enumValues].sort((a, b) => a.name.localeCompare(b.name)) : undefined,
    })).sort((a, b) => a.name.localeCompare(b.name)), // Sort types by name
  };

  return newSchema;
}

async function main() {
  await ensureDir(BASE_DIR);

  const newSchemaRaw = await fetchSchema();
  const cachedSchemaRaw = await loadCachedSchema();

  // Sort both schemas to ensure fields/types are in stable order before comparing
  const newSchema = sortSchema(newSchemaRaw);
  const cachedSchema = cachedSchemaRaw ? sortSchema(cachedSchemaRaw) : null;

  // Remove descriptions from schemas so we compare only structure, ignoring text changes
  const newStripped = stripDescriptions(newSchema);
  const cachedStripped = cachedSchema ? stripDescriptions(cachedSchema) : null;

  // Check if top-level root types (query, mutation, subscription) changed: added, removed, renamed
  const topChanged = !cachedStripped || topLevelChanged(cachedStripped, newStripped);

  // Deeply compare schema structure (types, fields, args, enums, inputs, etc.) without descriptions
  const typesChanged = !cachedStripped || !deepEqual(cachedStripped, newStripped);

  // Schema considered changed if either top-level or any structural changes detected
  const schemaChanged = topChanged || typesChanged;

  const hasCache = await cacheExists();
  const hasSchemaOverrides = await schemaOverridesExists();

  // Force regeneration if schema-overrides directory doesn't exist or is empty
  // even if schema hasn't changed, because we might have deleted the directory
  const shouldRegenerate = schemaChanged || !hasSchemaOverrides;

  if (!shouldRegenerate) {
    console.log("✅ Schema has not changed and schema-overrides directory exists. Skipping YAML generation.");
    return;
  }

  if (!hasCache) {
    console.log("🆕 No schema cache found. Creating cache and generating YAML files...");
  } else if (schemaChanged) {
    console.log("📌 Schema structure changed — regenerating YAML files...");
  } else if (!hasSchemaOverrides) {
    console.log("📁 Schema-overrides directory missing or empty — regenerating YAML files...");
  }

  await saveSchemaToCache(newSchemaRaw); // Save original schema (with descriptions) to cache

  // Generate YAML files for root Query operations (fields)
  await generateOperationYaml(newSchemaRaw, "queryType");
  await generateOperationYaml(newSchemaRaw, "mutationType");

  // Generate YAML files for all other types except internal ones and skip root query type itself
  const promises = newSchemaRaw.types
    .filter((type) => !type.name.startsWith("__")) // Skip internal types
    .filter((type) => newSchemaRaw.queryType?.name !== type.name) // Skip root query type itself
    .map((type) => generateTypeYaml(type));

  await Promise.all(promises);

  console.log("✅ YAML files generated in", BASE_DIR);
}

main().catch((e) => {
  console.error(e);
  process.exit(1);
});
